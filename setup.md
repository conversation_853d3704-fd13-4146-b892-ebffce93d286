# Image Drive Setup Guide

## Prerequisites

- Python 3.8 or higher
- Node.js 16 or higher
- MongoDB (local installation or MongoDB Atlas)

## Quick Setup

### 1. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Start MongoDB (if running locally)
# Make sure MongoDB is running on localhost:27017

# Run the backend server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Frontend Setup

```bash
# Navigate to frontend directory (in a new terminal)
cd frontend

# Install dependencies
npm install

# Start the development server
npm start
```

### 3. Access the Application

- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- API Documentation: http://localhost:8000/docs

## Environment Configuration

### Backend (.env file in backend directory)

```env
MONGODB_URL=mongodb://localhost:27017
DATABASE_NAME=image_drive
UPLOAD_DIR=uploads
MAX_FILE_SIZE=********
API_V1_STR=/api/v1
PROJECT_NAME=Image Drive API
```

### Frontend (.env file in frontend directory)

```env
REACT_APP_API_URL=http://localhost:8000/api/v1
```

## MongoDB Setup Options

### Option 1: Local MongoDB

1. Download and install MongoDB from https://www.mongodb.com/try/download/community
2. Start MongoDB service
3. Use default connection string: `mongodb://localhost:27017`

### Option 2: MongoDB Atlas (Cloud)

1. Create account at https://www.mongodb.com/atlas
2. Create a new cluster
3. Get connection string and update `MONGODB_URL` in backend/.env

## Features

- ✅ Drag & drop image upload
- ✅ Image gallery with grid layout
- ✅ Search functionality
- ✅ Image preview and download
- ✅ Tag and description management
- ✅ Responsive design
- ✅ File validation and error handling

## API Endpoints

- `POST /api/v1/images/upload` - Upload images
- `GET /api/v1/images` - Get all images
- `GET /api/v1/images/{id}` - Get specific image
- `PUT /api/v1/images/{id}` - Update image metadata
- `DELETE /api/v1/images/{id}` - Delete image
- `GET /api/v1/images/search` - Search images
- `GET /api/v1/images/{id}/download` - Download image

## Troubleshooting

### Backend Issues

1. **MongoDB Connection Error**: Ensure MongoDB is running and connection string is correct
2. **Port Already in Use**: Change port in uvicorn command: `--port 8001`
3. **Module Import Errors**: Ensure virtual environment is activated and dependencies installed

### Frontend Issues

1. **API Connection Error**: Check if backend is running on port 8000
2. **CORS Issues**: Backend is configured for localhost:3000, update if needed
3. **Build Errors**: Clear node_modules and reinstall: `rm -rf node_modules && npm install`

## Development

### Adding New Features

1. Backend: Add routes in `app/routes/`, models in `app/models/`, services in `app/services/`
2. Frontend: Add components in `src/components/`, update API service in `src/services/api.ts`

### Testing

- Backend: Run `pytest` (after installing pytest)
- Frontend: Run `npm test`

## Production Deployment

### Backend

1. Set environment variables for production
2. Use production WSGI server like Gunicorn
3. Configure reverse proxy (Nginx)
4. Use production MongoDB instance

### Frontend

1. Build production bundle: `npm run build`
2. Serve static files with web server
3. Update API URL for production backend

## Security Considerations

- Add authentication and authorization
- Implement rate limiting
- Add input validation and sanitization
- Use HTTPS in production
- Secure MongoDB with authentication
- Add file type and size restrictions
