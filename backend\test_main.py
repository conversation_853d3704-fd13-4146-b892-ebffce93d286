import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)

def test_root():
    """Test the root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    assert response.json() == {"message": "Welcome to Image Drive API"}

def test_health_check():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json() == {"status": "healthy"}

def test_get_images():
    """Test getting images (should work even with empty database)"""
    response = client.get("/api/v1/images")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_upload_image_no_file():
    """Test upload endpoint without file"""
    response = client.post("/api/v1/images/upload")
    assert response.status_code == 422  # Validation error

def test_get_nonexistent_image():
    """Test getting a non-existent image"""
    response = client.get("/api/v1/images/nonexistent")
    assert response.status_code == 404

def test_search_images():
    """Test search endpoint"""
    response = client.get("/api/v1/images/search?q=test")
    assert response.status_code == 200
    assert isinstance(response.json(), list)
