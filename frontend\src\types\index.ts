export interface Image {
  id: string;
  filename: string;
  original_filename: string;
  content_type: string;
  size: number;
  tags: string[];
  description?: string;
  upload_date: string;
  url: string;
}

export interface ImageUploadData {
  tags?: string;
  description?: string;
}

export interface ImageUpdateData {
  tags?: string[];
  description?: string;
}

export interface UploadProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  result?: Image;
}
