import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import {
  Box,
  Paper,
  Typography,
  Button,
  TextField,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
} from '@mui/material';
import {
  CloudUpload,
  Close,
  Add,
} from '@mui/icons-material';
import { imageService } from '../services/api';
import { Image, UploadProgress } from '../types';

interface ImageUploadProps {
  onUploadComplete: (images: Image[]) => void;
}

const ImageUpload: React.FC<ImageUploadProps> = ({ onUploadComplete }) => {
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [tags, setTags] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [error, setError] = useState<string>('');

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setError('');
    
    const newUploads: UploadProgress[] = acceptedFiles.map(file => ({
      file,
      progress: 0,
      status: 'uploading' as const,
    }));

    setUploads(prev => [...prev, ...newUploads]);

    // Upload files
    acceptedFiles.forEach(async (file, index) => {
      try {
        const uploadData = {
          tags: tags.trim(),
          description: description.trim(),
        };

        const result = await imageService.uploadImage(file, uploadData);
        
        setUploads(prev => prev.map((upload, i) => 
          upload.file === file 
            ? { ...upload, status: 'completed', progress: 100, result }
            : upload
        ));

      } catch (err: any) {
        setUploads(prev => prev.map((upload, i) => 
          upload.file === file 
            ? { ...upload, status: 'error', error: err.message || 'Upload failed' }
            : upload
        ));
      }
    });
  }, [tags, description]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.bmp', '.webp']
    },
    multiple: true,
  });

  const handleComplete = () => {
    const completedImages = uploads
      .filter(upload => upload.status === 'completed' && upload.result)
      .map(upload => upload.result!);
    
    if (completedImages.length > 0) {
      onUploadComplete(completedImages);
    }
    
    setUploads([]);
    setTags('');
    setDescription('');
  };

  const removeUpload = (fileToRemove: File) => {
    setUploads(prev => prev.filter(upload => upload.file !== fileToRemove));
  };

  const completedUploads = uploads.filter(upload => upload.status === 'completed');
  const hasUploads = uploads.length > 0;
  const allCompleted = uploads.length > 0 && uploads.every(upload => upload.status === 'completed');

  return (
    <Box sx={{ mb: 4 }}>
      <Paper
        {...getRootProps()}
        sx={{
          p: 4,
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
          cursor: 'pointer',
          textAlign: 'center',
          mb: 2,
        }}
      >
        <input {...getInputProps()} />
        <CloudUpload sx={{ fontSize: 48, color: 'grey.400', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {isDragActive ? 'Drop images here' : 'Drag & drop images here, or click to select'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Supports: JPEG, PNG, GIF, BMP, WebP
        </Typography>
      </Paper>

      <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
        <TextField
          label="Tags (comma-separated)"
          value={tags}
          onChange={(e) => setTags(e.target.value)}
          placeholder="nature, landscape, photography"
          fullWidth
        />
        <TextField
          label="Description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Describe your images..."
          fullWidth
        />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {hasUploads && (
        <Paper sx={{ p: 2, mb: 2 }}>
          <Typography variant="h6" gutterBottom>
            Upload Progress ({completedUploads.length}/{uploads.length})
          </Typography>
          
          {uploads.map((upload, index) => (
            <Box key={index} sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" sx={{ flexGrow: 1 }}>
                  {upload.file.name}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => removeUpload(upload.file)}
                >
                  <Close />
                </IconButton>
              </Box>
              
              {upload.status === 'uploading' && (
                <LinearProgress />
              )}
              
              {upload.status === 'completed' && (
                <Alert severity="success">Upload completed</Alert>
              )}
              
              {upload.status === 'error' && (
                <Alert severity="error">{upload.error}</Alert>
              )}
            </Box>
          ))}

          {allCompleted && (
            <Button
              variant="contained"
              onClick={handleComplete}
              startIcon={<Add />}
              fullWidth
            >
              Add {completedUploads.length} Image{completedUploads.length !== 1 ? 's' : ''} to Gallery
            </Button>
          )}
        </Paper>
      )}
    </Box>
  );
};

export default ImageUpload;
