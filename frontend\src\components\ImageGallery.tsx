import React, { useState } from 'react';
import {
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Typography,
  IconButton,
  Chip,
  Box,
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  TextField,
} from '@mui/material';
import {
  Delete,
  Download,
  Edit,
  Visibility,
  Close,
} from '@mui/icons-material';
import { Image } from '../types';
import { imageService } from '../services/api';

interface ImageGalleryProps {
  images: Image[];
  onImageDeleted: (imageId: string) => void;
  onImageUpdated: (image: Image) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  onImageDeleted,
  onImageUpdated,
}) => {
  const [selectedImage, setSelectedImage] = useState<Image | null>(null);
  const [editingImage, setEditingImage] = useState<Image | null>(null);
  const [editTags, setEditTags] = useState<string>('');
  const [editDescription, setEditDescription] = useState<string>('');

  const handleDelete = async (imageId: string) => {
    if (window.confirm('Are you sure you want to delete this image?')) {
      try {
        await imageService.deleteImage(imageId);
        onImageDeleted(imageId);
      } catch (error) {
        console.error('Failed to delete image:', error);
        alert('Failed to delete image');
      }
    }
  };

  const handleDownload = (image: Image) => {
    const link = document.createElement('a');
    link.href = imageService.getDownloadUrl(image.id);
    link.download = image.original_filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleEditStart = (image: Image) => {
    setEditingImage(image);
    setEditTags(image.tags.join(', '));
    setEditDescription(image.description || '');
  };

  const handleEditSave = async () => {
    if (!editingImage) return;

    try {
      const updatedImage = await imageService.updateImage(editingImage.id, {
        tags: editTags.split(',').map(tag => tag.trim()).filter(tag => tag),
        description: editDescription.trim() || undefined,
      });
      
      onImageUpdated(updatedImage);
      setEditingImage(null);
    } catch (error) {
      console.error('Failed to update image:', error);
      alert('Failed to update image');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  if (images.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <Typography variant="h6" color="text.secondary">
          No images uploaded yet
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Upload some images to get started!
        </Typography>
      </Box>
    );
  }

  return (
    <>
      <Grid container spacing={3}>
        {images.map((image) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={image.id}>
            <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardMedia
                component="img"
                height="200"
                image={imageService.getImageUrl(image.filename)}
                alt={image.original_filename}
                sx={{ cursor: 'pointer', objectFit: 'cover' }}
                onClick={() => setSelectedImage(image)}
              />
              
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h6" noWrap title={image.original_filename}>
                  {image.original_filename}
                </Typography>
                
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  {formatFileSize(image.size)} • {formatDate(image.upload_date)}
                </Typography>
                
                {image.description && (
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    {image.description}
                  </Typography>
                )}
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {image.tags.map((tag, index) => (
                    <Chip key={index} label={tag} size="small" />
                  ))}
                </Box>
              </CardContent>
              
              <CardActions>
                <IconButton
                  size="small"
                  onClick={() => setSelectedImage(image)}
                  title="View"
                >
                  <Visibility />
                </IconButton>
                
                <IconButton
                  size="small"
                  onClick={() => handleEditStart(image)}
                  title="Edit"
                >
                  <Edit />
                </IconButton>
                
                <IconButton
                  size="small"
                  onClick={() => handleDownload(image)}
                  title="Download"
                >
                  <Download />
                </IconButton>
                
                <IconButton
                  size="small"
                  onClick={() => handleDelete(image.id)}
                  title="Delete"
                  color="error"
                >
                  <Delete />
                </IconButton>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {/* Image Preview Dialog */}
      <Dialog
        open={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        maxWidth="lg"
        fullWidth
      >
        {selectedImage && (
          <>
            <DialogContent>
              <Box sx={{ textAlign: 'center' }}>
                <img
                  src={imageService.getImageUrl(selectedImage.filename)}
                  alt={selectedImage.original_filename}
                  style={{ maxWidth: '100%', maxHeight: '70vh', objectFit: 'contain' }}
                />
                
                <Typography variant="h6" sx={{ mt: 2 }}>
                  {selectedImage.original_filename}
                </Typography>
                
                <Typography variant="body2" color="text.secondary">
                  {formatFileSize(selectedImage.size)} • {formatDate(selectedImage.upload_date)}
                </Typography>
                
                {selectedImage.description && (
                  <Typography variant="body1" sx={{ mt: 1 }}>
                    {selectedImage.description}
                  </Typography>
                )}
                
                <Box sx={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                  {selectedImage.tags.map((tag, index) => (
                    <Chip key={index} label={tag} size="small" />
                  ))}
                </Box>
              </Box>
            </DialogContent>
            
            <DialogActions>
              <Button onClick={() => handleDownload(selectedImage)} startIcon={<Download />}>
                Download
              </Button>
              <Button onClick={() => setSelectedImage(null)} startIcon={<Close />}>
                Close
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Edit Dialog */}
      <Dialog
        open={!!editingImage}
        onClose={() => setEditingImage(null)}
        maxWidth="sm"
        fullWidth
      >
        {editingImage && (
          <>
            <DialogContent>
              <Typography variant="h6" gutterBottom>
                Edit {editingImage.original_filename}
              </Typography>
              
              <TextField
                label="Tags (comma-separated)"
                value={editTags}
                onChange={(e) => setEditTags(e.target.value)}
                fullWidth
                margin="normal"
              />
              
              <TextField
                label="Description"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                fullWidth
                multiline
                rows={3}
                margin="normal"
              />
            </DialogContent>
            
            <DialogActions>
              <Button onClick={() => setEditingImage(null)}>Cancel</Button>
              <Button onClick={handleEditSave} variant="contained">Save</Button>
            </DialogActions>
          </>
        )}
      </Dialog>
    </>
  );
};

export default ImageGallery;
