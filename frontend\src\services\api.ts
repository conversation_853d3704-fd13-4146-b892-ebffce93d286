import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface Image {
  id: string;
  filename: string;
  original_filename: string;
  content_type: string;
  size: number;
  tags: string[];
  description?: string;
  upload_date: string;
  url: string;
}

export interface ImageUploadData {
  tags?: string;
  description?: string;
}

export interface ImageUpdateData {
  tags?: string[];
  description?: string;
}

class ImageService {
  async uploadImage(file: File, data?: ImageUploadData): Promise<Image> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (data?.tags) {
      formData.append('tags', data.tags);
    }
    
    if (data?.description) {
      formData.append('description', data.description);
    }

    const response = await api.post('/images/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  }

  async getImages(skip: number = 0, limit: number = 50): Promise<Image[]> {
    const response = await api.get('/images', {
      params: { skip, limit },
    });
    return response.data;
  }

  async getImage(imageId: string): Promise<Image> {
    const response = await api.get(`/images/${imageId}`);
    return response.data;
  }

  async updateImage(imageId: string, data: ImageUpdateData): Promise<Image> {
    const response = await api.put(`/images/${imageId}`, data);
    return response.data;
  }

  async deleteImage(imageId: string): Promise<void> {
    await api.delete(`/images/${imageId}`);
  }

  async searchImages(query: string, skip: number = 0, limit: number = 50): Promise<Image[]> {
    const response = await api.get('/images/search', {
      params: { q: query, skip, limit },
    });
    return response.data;
  }

  getImageUrl(filename: string): string {
    return `${API_BASE_URL.replace('/api/v1', '')}/uploads/${filename}`;
  }

  getDownloadUrl(imageId: string): string {
    return `${API_BASE_URL}/images/${imageId}/download`;
  }
}

export const imageService = new ImageService();
export default api;
