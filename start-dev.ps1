# Image Drive Development Startup Script
Write-Host "Starting Image Drive Development Environment..." -ForegroundColor Green

# Check if MongoDB is running
Write-Host "Checking MongoDB connection..." -ForegroundColor Yellow
try {
    $mongoTest = Invoke-WebRequest -Uri "http://localhost:27017" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "MongoDB is running!" -ForegroundColor Green
} catch {
    Write-Host "Warning: MongoDB might not be running on localhost:27017" -ForegroundColor Red
    Write-Host "Please start MongoDB or update the connection string in backend/.env" -ForegroundColor Yellow
}

# Start Backend
Write-Host "Starting Backend Server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'backend'; if (Test-Path 'venv\Scripts\activate.ps1') { .\venv\Scripts\activate.ps1 } else { Write-Host 'Virtual environment not found. Please run: python -m venv venv' -ForegroundColor Red }; uvicorn app.main:app --reload --host 0.0.0.0 --port 8000"

# Wait a moment for backend to start
Start-Sleep -Seconds 3

# Start Frontend
Write-Host "Starting Frontend Server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'frontend'; npm start"

Write-Host "Development servers are starting..." -ForegroundColor Green
Write-Host "Frontend: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Backend API: http://localhost:8000" -ForegroundColor Cyan
Write-Host "API Docs: http://localhost:8000/docs" -ForegroundColor Cyan
