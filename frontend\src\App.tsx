import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  AppBar,
  <PERSON><PERSON>bar,
  CssBaseline,
  ThemeProvider,
  createTheme,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  PhotoLibrary,
} from '@mui/icons-material';

import ImageUpload from './components/ImageUpload';
import ImageGallery from './components/ImageGallery';
import SearchBar from './components/SearchBar';
import { Image } from './types';
import { imageService } from './services/api';

const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

function App() {
  const [images, setImages] = useState<Image[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    loadImages();
  }, []);

  const loadImages = async () => {
    try {
      setLoading(true);
      setError('');
      const fetchedImages = await imageService.getImages();
      setImages(fetchedImages);
    } catch (err: any) {
      setError('Failed to load images. Make sure the backend server is running.');
      console.error('Failed to load images:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleUploadComplete = (newImages: Image[]) => {
    setImages(prev => [...newImages, ...prev]);
  };

  const handleImageDeleted = (imageId: string) => {
    setImages(prev => prev.filter(img => img.id !== imageId));
  };

  const handleImageUpdated = (updatedImage: Image) => {
    setImages(prev => prev.map(img =>
      img.id === updatedImage.id ? updatedImage : img
    ));
  };

  const handleSearch = async (query: string) => {
    try {
      setLoading(true);
      setError('');
      setSearchQuery(query);
      const searchResults = await imageService.searchImages(query);
      setImages(searchResults);
    } catch (err: any) {
      setError('Failed to search images');
      console.error('Failed to search images:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    loadImages();
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppBar position="static">
        <Toolbar>
          <PhotoLibrary sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Image Drive
          </Typography>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          My Images
        </Typography>

        <ImageUpload onUploadComplete={handleUploadComplete} />

        <SearchBar onSearch={handleSearch} onClear={handleClearSearch} />

        {searchQuery && (
          <Typography variant="body1" sx={{ mb: 2 }}>
            Search results for: <strong>"{searchQuery}"</strong>
          </Typography>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <ImageGallery
            images={images}
            onImageDeleted={handleImageDeleted}
            onImageUpdated={handleImageUpdated}
          />
        )}
      </Container>
    </ThemeProvider>
  );
}

export default App;
